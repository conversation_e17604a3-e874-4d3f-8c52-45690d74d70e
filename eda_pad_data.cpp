/*
 * Qt-based reimplementation of KiCad PAD class - Implementation
 * 
 * This file contains the implementation of EDA_PAD_DATA class providing
 * comprehensive pad functionality using Qt frameworks.
 */

#include "eda_pad_data.h"
#include "eda_padstack_data.h"
#include "eda_board_data.h"
#include "eda_footprint_data.h"
#include "eda_brd_shape_data.h"
#include "qt_temporary_implementations.h"

#include <QtCore/QDebug>
#include <QtCore/QJsonObject>
#include <QtCore/QJsonArray>
#include <QtCore/QMutexLocker>
#include <QtCore/QCoreApplication>
#include <QtCore/QLoggingCategory>
#include <QtCore/QObject>
#include <QtGui/QTransform>
#include <cmath>
#include <algorithm>

// Define logging category
Q_LOGGING_CATEGORY(lcEDA_PAD_DATA, "eda.pad")



//=============================================================================
// EDA_PAD_DATA Implementation
//=============================================================================

EDA_PAD_DATA::EDA_PAD_DATA(EDA_FOOTPRINT_DATA* parent)
    : EDA_BOARD_CONNECTED_OBJECT(parent, QtKicadType::PcbPadT)
    , m_position(0.0, 0.0)
    , m_attribute(QtPadAttribute::PTH)
    , m_property(QtPadProperty::None)
    , m_padToDieLength(0)
    , m_padStack()  // Initialize padstack
    , m_subRatsnest(0)
{
    
    // Initialize default padstack
    //m_padStack.setLayerSet( PTHMask() );
}

EDA_PAD_DATA::EDA_PAD_DATA(const EDA_PAD_DATA& other)
    : EDA_BOARD_CONNECTED_OBJECT(other)
    , m_number(other.m_number)
    , m_pinFunction(other.m_pinFunction)
    , m_pinType(other.m_pinType)
    , m_position(other.m_position)
    , m_attribute(other.m_attribute)
    , m_property(other.m_property)
    , m_padToDieLength(other.m_padToDieLength)
    , m_padStack(other.m_padStack)
    , m_subRatsnest(other.m_subRatsnest)
    , m_zoneLayerOverrides(other.m_zoneLayerOverrides)
{
    
    // Reset dirty flags - shapes will be rebuilt when needed
    m_shapesDirty = true;
    m_polyDirty = true;
}

EDA_PAD_DATA::~EDA_PAD_DATA()
{
    clearCaches();
}

EDA_PAD_DATA& EDA_PAD_DATA::operator=(const EDA_PAD_DATA& other)
{
    if (this != &other) {
        EDA_BOARD_CONNECTED_OBJECT::operator=(other);
        
        m_number = other.m_number;
        m_pinFunction = other.m_pinFunction;
        m_pinType = other.m_pinType;
        m_position = other.m_position;
        m_attribute = other.m_attribute;
        m_property = other.m_property;
        m_padToDieLength = other.m_padToDieLength;
        m_padStack = other.m_padStack;
        m_subRatsnest = other.m_subRatsnest;
        m_zoneLayerOverrides = other.m_zoneLayerOverrides;
        
        setDirty();
    }
    return *this;
}

bool EDA_PAD_DATA::classOf(const EDA_OBJECT_DATA* item)
{
    return item && item->getItemType() == QtKicadType::PcbPadT;
}

//=============================================================================
// Basic Properties
//=============================================================================

void EDA_PAD_DATA::setNumber(const QString& number)
{
    if (m_number != number) {
        m_number = number;
        // Number changed
    }
}

bool EDA_PAD_DATA::canHaveNumber() const
{
    return m_attribute != QtPadAttribute::NPTH && !isAperturePad();
}

void EDA_PAD_DATA::setPinFunction(const QString& function)
{
    if (m_pinFunction != function) {
        m_pinFunction = function;

    }
}

void EDA_PAD_DATA::setPinType(const QString& type)
{
    if (m_pinType != type) {
        m_pinType = type;
        // Signal emission removed
        // Signal emission removed
    }
}

void EDA_PAD_DATA::setAttribute(QtPadAttribute attribute)
{
    if (m_attribute != attribute) {
        m_attribute = attribute;
        
        // Update layer set based on attribute
        switch (attribute) {
        case QtPadAttribute::PTH:
            setLayerSet(PTHMask());
            break;
        case QtPadAttribute::SMD:
            setLayerSet(SMDMask());
            break;
        case QtPadAttribute::Connector:
            setLayerSet(ConnectorSMDMask());
            break;
        case QtPadAttribute::NPTH:
            setLayerSet(UnplatedHoleMask());
            break;
        }
        
        setDirty();
        // Signal emission removed
        // Signal emission removed
    }
}

void EDA_PAD_DATA::setProperty(QtPadProperty property)
{
    if (m_property != property) {
        m_property = property;
        // Signal emission removed
        // Signal emission removed
    }
}

//=============================================================================
// Position and Orientation
//=============================================================================

void EDA_PAD_DATA::setPosition(const QPointF& position)
{
    if (m_position != position) {
        m_position = position;
        setDirty();
        notifyGeometryChange();
        // Signal emission removed
    }
}

void EDA_PAD_DATA::setOrientation(double degrees)
{
    double normalizedAngle = std::fmod(degrees, 360.0);
    if (normalizedAngle < 0.0) {
        normalizedAngle += 360.0;
    }
    
    if (!qFuzzyCompare(m_padStack.orientation(), normalizedAngle)) {
        m_padStack.setOrientation(normalizedAngle);
        setDirty();
        notifyGeometryChange();
    }
}

double EDA_PAD_DATA::getFPRelativeOrientation() const
{
    EDA_FOOTPRINT_DATA* footprint = getParentFootprint();
    if (footprint) {
        // return getOrientation() - footprint->getOrientation();
        return getOrientation(); // Simplified for now
    }
    return getOrientation();
}

void EDA_PAD_DATA::setFPRelativeOrientation(double degrees)
{
    EDA_FOOTPRINT_DATA* footprint = getParentFootprint();
    if (footprint) {
        // setOrientation(degrees + footprint->getOrientation());
        setOrientation(degrees); // Simplified for now
    } else {
        setOrientation(degrees);
    }
}

//=============================================================================
// Padstack Management
//=============================================================================

void EDA_PAD_DATA::setPadStack(const EDA_PADSTACK_DATA& padStack)
{
    if (!(m_padStack == padStack)) {
        m_padStack = padStack;
        setDirty();
        notifyGeometryChange();
        // Shape and size changed
        // Signal emission removed
    }
}

//=============================================================================
// Shape Properties
//=============================================================================

QtPadShape EDA_PAD_DATA::getShape(QtPcbLayerId layer) const
{
    return m_padStack.shape(layer);
}

void EDA_PAD_DATA::setShape(QtPadShape shape, QtPcbLayerId layer)
{
    if (m_padStack.shape(layer) != shape) {
        m_padStack.setShape(shape, layer);
        setDirty();
        notifyGeometryChange();
        // Signal emission removed
    }
}

QSizeF EDA_PAD_DATA::getSize(QtPcbLayerId layer) const
{
    return m_padStack.size(layer);
}

void EDA_PAD_DATA::setSize(const QSizeF& size, QtPcbLayerId layer)
{
    if (m_padStack.size(layer) != size) {
        m_padStack.setSize(size, layer);
        setDirty();
        notifyGeometryChange();
        // Signal emission removed
    }
}

QPointF EDA_PAD_DATA::getOffset(QtPcbLayerId layer) const
{
    return m_padStack.offset(layer);
}

void EDA_PAD_DATA::setOffset(const QPointF& offset, QtPcbLayerId layer)
{
    if (m_padStack.offset(layer) != offset) {
        m_padStack.setOffset(offset, layer);
        setDirty();
        notifyGeometryChange();
    }
}

QPointF EDA_PAD_DATA::shapePosition(QtPcbLayerId layer) const
{
    QPointF offset = getOffset(layer);
    
    // Apply rotation to offset
    double angle = qDegreesToRadians(getOrientation());
    double cos_a = std::cos(angle);
    double sin_a = std::sin(angle);
    
    QPointF rotatedOffset(
        offset.x() * cos_a - offset.y() * sin_a,
        offset.x() * sin_a + offset.y() * cos_a
    );
    
    return m_position + rotatedOffset;
}

//=============================================================================
// Drill Properties
//=============================================================================

bool EDA_PAD_DATA::hasHole() const
{
    return m_padStack.hasPrimaryDrill();
}

bool EDA_PAD_DATA::isDrilledHole() const
{
    return hasHole() && m_padStack.drill().shape == QtPadDrillShape::Circle;
}

void EDA_PAD_DATA::setDrillSize(const QSizeF& size)
{
    QtDrillProperties drill = m_padStack.drill();
    if (drill.size != size) {
        drill.size = size;
        m_padStack.setDrill(drill);
        setDirty();
        notifyGeometryChange();
        // Signal emission removed
    }
}

void EDA_PAD_DATA::setDrillShape(QtPadDrillShape shape)
{
    QtDrillProperties drill = m_padStack.drill();
    if (drill.shape != shape) {
        drill.shape = shape;
        m_padStack.setDrill(drill);
        setDirty();
        notifyGeometryChange();
        // Signal emission removed
    }
}

//=============================================================================
// Layer Management
//=============================================================================

void EDA_PAD_DATA::setLayerSet(const QtLayerSet& layers)
{
    if (!(m_padStack.layerSet() == layers)) {
        m_padStack.setLayerSet(layers);
        setDirty();
        // Signal emission removed
        // Signal emission removed
    }
}

bool EDA_PAD_DATA::isOnCopperLayer() const
{
    QtLayerSet copperLayers = QtLayerSet::allCopperLayers();
    return !(m_padStack.layerSet() & copperLayers).empty();
}

QtPcbLayerId EDA_PAD_DATA::getPrincipalLayer() const
{
    QtLayerSet layers = m_padStack.layerSet();
    
    // For SMD pads, return the copper layer they're on
    if (m_attribute == QtPadAttribute::SMD || m_attribute == QtPadAttribute::Connector) {
        if (layers.test(QtPcbLayerId::FCu)) {
            return QtPcbLayerId::FCu;
        }
        if (layers.test(QtPcbLayerId::BCu)) {
            return QtPcbLayerId::BCu;
        }
    }
    
    // For PTH pads, return front copper
    return QtPcbLayerId::FCu;
}

//=============================================================================
// Parent and Relationships
//=============================================================================

EDA_FOOTPRINT_DATA* EDA_PAD_DATA::getParentFootprint() const
{
    EDA_BOARD_OBJECT_CONTAINER* parent = getParent();
    return dynamic_cast<EDA_FOOTPRINT_DATA*>(parent);
}

bool EDA_PAD_DATA::sameLogicalPadAs(const EDA_PAD_DATA* other) const
{
    if (!other) return false;
    
    return getParentFootprint() == other->getParentFootprint() &&
           !m_number.isEmpty() && m_number == other->m_number;
}

bool EDA_PAD_DATA::sharesNetTieGroup(const EDA_PAD_DATA* other) const
{
    // TODO: Implement net-tie group logic
    Q_UNUSED(other)
    return false;
}

bool EDA_PAD_DATA::isNoConnectPad() const
{
    // TODO: Implement no-connect logic
    return false;
}

bool EDA_PAD_DATA::isFreePad() const
{
    // TODO: Implement free pad logic
    return false;
}

bool EDA_PAD_DATA::isFlipped() const
{
    EDA_FOOTPRINT_DATA* footprint = getParentFootprint();
    if (footprint) {
        // return footprint->isFlipped();
        return false; // Simplified for now
    }
    return false;
}

//=============================================================================
// Geometric Transformations
//=============================================================================

void EDA_PAD_DATA::move(const QPointF& moveVector)
{
    setPosition(m_position + moveVector);
}

void EDA_PAD_DATA::rotate(const QPointF& rotationCenter, double angleRadians)
{
    double angleDegrees = qRadiansToDegrees(angleRadians);
    
    // Rotate position around center
    QPointF translated = m_position - rotationCenter;
    double cos_a = std::cos(angleRadians);
    double sin_a = std::sin(angleRadians);
    
    QPointF rotated(
        translated.x() * cos_a - translated.y() * sin_a,
        translated.x() * sin_a + translated.y() * cos_a
    );
    
    setPosition(rotationCenter + rotated);
    
    // Update orientation
    setOrientation(getOrientation() + angleDegrees);
}

void EDA_PAD_DATA::flip(const QPointF& center, QtFlipDirection flipDirection)
{
    Q_UNUSED(center)
    Q_UNUSED(flipDirection)
    
    // TODO: Implement flip transformation
    setDirty();
    notifyGeometryChange();
}

//=============================================================================
// Rounded Rectangle Properties
//=============================================================================

double EDA_PAD_DATA::getRoundRectCornerRadius(QtPcbLayerId layer) const
{
    return m_padStack.roundRectRadius(layer);
}

void EDA_PAD_DATA::setRoundRectCornerRadius(QtPcbLayerId layer, double radius)
{
    if (!qFuzzyCompare(m_padStack.roundRectRadius(layer), radius)) {
        m_padStack.setRoundRectRadius(radius, layer);
        setDirty();
        notifyGeometryChange();
    }
}

double EDA_PAD_DATA::getRoundRectRadiusRatio(QtPcbLayerId layer) const
{
    return m_padStack.roundRectRadiusRatio(layer);
}

void EDA_PAD_DATA::setRoundRectRadiusRatio(QtPcbLayerId layer, double ratio)
{
    if (!qFuzzyCompare(m_padStack.roundRectRadiusRatio(layer), ratio)) {
        m_padStack.setRoundRectRadiusRatio(qBound(0.0, ratio, 0.5), layer);
        setDirty();
        notifyGeometryChange();
    }
}

//=============================================================================
// Chamfered Rectangle Properties
//=============================================================================

double EDA_PAD_DATA::getChamferRatio(QtPcbLayerId layer) const
{
    return m_padStack.chamferRatio(layer);
}

void EDA_PAD_DATA::setChamferRatio(QtPcbLayerId layer, double ratio)
{
    if (!qFuzzyCompare(m_padStack.chamferRatio(layer), ratio)) {
        m_padStack.setChamferRatio(qBound(0.0, ratio, 0.5), layer);
        setDirty();
        notifyGeometryChange();
    }
}

QtChamferPositions EDA_PAD_DATA::getChamferPositions(QtPcbLayerId layer) const
{
    return m_padStack.chamferPositions(layer);
}

void EDA_PAD_DATA::setChamferPositions(QtPcbLayerId layer, QtChamferPositions positions)
{
    if (m_padStack.chamferPositions(layer) != positions) {
        m_padStack.setChamferPositions(positions, layer);
        setDirty();
        notifyGeometryChange();
    }
}

//=============================================================================
// Trapezoid Properties
//=============================================================================

QSizeF EDA_PAD_DATA::getTrapezoidDelta(QtPcbLayerId layer) const
{
    return m_padStack.trapezoidDeltaSize(layer);
}

void EDA_PAD_DATA::setTrapezoidDelta(QtPcbLayerId layer, const QSizeF& delta)
{
    if (m_padStack.trapezoidDeltaSize(layer) != delta) {
        m_padStack.setTrapezoidDeltaSize(delta, layer);
        setDirty();
        notifyGeometryChange();
    }
}

//=============================================================================
// Custom Shape Support
//=============================================================================

QtPadShape EDA_PAD_DATA::getAnchorPadShape(QtPcbLayerId layer) const
{
    return m_padStack.anchorShape(layer);
}

void EDA_PAD_DATA::setAnchorPadShape(QtPcbLayerId layer, QtPadShape shape)
{
    if (m_padStack.anchorShape(layer) != shape) {
        m_padStack.setAnchorShape(shape, layer);
        setDirty();
        notifyGeometryChange();
    }
}

QtCustomShapeZoneMode EDA_PAD_DATA::getCustomShapeZoneMode() const
{
    return m_padStack.customShapeZoneMode();
}

void EDA_PAD_DATA::setCustomShapeZoneMode(QtCustomShapeZoneMode mode)
{
    if (m_padStack.customShapeZoneMode() != mode) {
        m_padStack.setCustomShapeZoneMode(mode);
        setDirty();
        // Signal emission removed
    }
}

QList<QSharedPointer<EDA_BRD_SHAPE_DATA>> EDA_PAD_DATA::getPrimitives(QtPcbLayerId layer) const
{
    return m_padStack.primitives(layer);
}

void EDA_PAD_DATA::addPrimitive(QtPcbLayerId layer, QSharedPointer<EDA_BRD_SHAPE_DATA> primitive)
{
    if (primitive) {
        m_padStack.addPrimitive(primitive, layer);
        setDirty();
        // Signal emission removed
    }
}

void EDA_PAD_DATA::replacePrimitives(QtPcbLayerId layer, const QList<QSharedPointer<EDA_BRD_SHAPE_DATA>>& primitives)
{
    m_padStack.replacePrimitives(primitives, layer);
    setDirty();
    // Signal emission removed
}

void EDA_PAD_DATA::appendPrimitives(QtPcbLayerId layer, const QList<QSharedPointer<EDA_BRD_SHAPE_DATA>>& primitives)
{
    m_padStack.appendPrimitives(primitives, layer);
    setDirty();
    // Signal emission removed
}

void EDA_PAD_DATA::deletePrimitivesList(QtPcbLayerId layer)
{
    m_padStack.clearPrimitives(layer);
    setDirty();
    // Signal emission removed
}

void EDA_PAD_DATA::mergePrimitivesAsPolygon(QtPcbLayerId layer, QtShapePolySet* mergedPolygon,
                                     QtErrorLoc errorLocation) const
{
    Q_UNUSED(layer)
    Q_UNUSED(mergedPolygon)
    Q_UNUSED(errorLocation)
    
    // TODO: Implement primitive merging logic
    qCWarning(lcEDA_PAD_DATA) << "mergePrimitivesAsPolygon not yet implemented";
}

//=============================================================================
// Clearance and Margins
//=============================================================================

std::optional<int> EDA_PAD_DATA::getLocalClearance() const
{
    return m_padStack.clearance(QtPcbLayerId::FCu);
}

void EDA_PAD_DATA::setLocalClearance(std::optional<int> clearance)
{
    if (m_padStack.clearance(QtPcbLayerId::FCu) != clearance) {
        m_padStack.setClearance(clearance, QtPcbLayerId::FCu);
        // Signal emission removed
    }
}

std::optional<int> EDA_PAD_DATA::getLocalSolderMaskMargin() const
{
    return m_padStack.solderMaskMargin(QtPcbLayerId::FCu);
}

void EDA_PAD_DATA::setLocalSolderMaskMargin(std::optional<int> margin)
{
    if (m_padStack.solderMaskMargin(QtPcbLayerId::FCu) != margin) {
        m_padStack.setSolderMaskMargin(margin, QtPcbLayerId::FCu);
        // Signal emission removed
    }
}

std::optional<int> EDA_PAD_DATA::getLocalSolderPasteMargin() const
{
    return m_padStack.solderPasteMargin(QtPcbLayerId::FCu);
}

void EDA_PAD_DATA::setLocalSolderPasteMargin(std::optional<int> margin)
{
    if (m_padStack.solderPasteMargin(QtPcbLayerId::FCu) != margin) {
        m_padStack.setSolderPasteMargin(margin, QtPcbLayerId::FCu);
        // Signal emission removed
    }
}

std::optional<double> EDA_PAD_DATA::getLocalSolderPasteMarginRatio() const
{
    return m_padStack.solderPasteMarginRatio(QtPcbLayerId::FCu);
}

void EDA_PAD_DATA::setLocalSolderPasteMarginRatio(std::optional<double> ratio)
{
    if (m_padStack.solderPasteMarginRatio(QtPcbLayerId::FCu) != ratio) {
        m_padStack.setSolderPasteMarginRatio(ratio, QtPcbLayerId::FCu);
        // Signal emission removed
    }
}

int EDA_PAD_DATA::getSolderMaskExpansion(QtPcbLayerId layer) const
{
    Q_UNUSED(layer)
    
    std::optional<int> local = getLocalSolderMaskMargin();
    if (local.has_value()) {
        return local.value();
    }
    
    // TODO: Get from parent footprint or global settings
    return 0;
}

QSizeF EDA_PAD_DATA::getSolderPasteMargin(QtPcbLayerId layer) const
{
    Q_UNUSED(layer)
    
    std::optional<int> margin = getLocalSolderPasteMargin();
    std::optional<double> ratio = getLocalSolderPasteMarginRatio();
    
    QSizeF size = getSize();
    QSizeF result(0.0, 0.0);
    
    if (margin.has_value()) {
        result = QSizeF(margin.value(), margin.value());
    }
    
    if (ratio.has_value()) {
        result += QSizeF(size.width() * ratio.value(), size.height() * ratio.value());
    }
    
    return result;
}

//=============================================================================
// Zone Connection and Thermal Management
//=============================================================================

QtZoneConnection EDA_PAD_DATA::getLocalZoneConnection() const
{
    return m_padStack.zoneConnection(QtPcbLayerId::FCu).value_or(QtZoneConnection::INHERITED);
}

void EDA_PAD_DATA::setLocalZoneConnection(QtZoneConnection connection)
{
    if (m_padStack.zoneConnection(QtPcbLayerId::FCu) != connection) {
        m_padStack.setZoneConnection(connection, QtPcbLayerId::FCu);
        // Signal emission removed
    }
}

std::optional<int> EDA_PAD_DATA::getLocalThermalSpokeWidth() const
{
    return m_padStack.thermalSpokeWidth(QtPcbLayerId::FCu);
}

void EDA_PAD_DATA::setLocalThermalSpokeWidth(std::optional<int> width)
{
    if (m_padStack.thermalSpokeWidth(QtPcbLayerId::FCu) != width) {
        m_padStack.setThermalSpokeWidth(width, QtPcbLayerId::FCu);
        // Signal emission removed
    }
}

double EDA_PAD_DATA::getThermalSpokeAngle() const
{
    return m_padStack.thermalSpokeAngle(QtPcbLayerId::FCu);
}

void EDA_PAD_DATA::setThermalSpokeAngle(double degrees)
{
    if (!qFuzzyCompare(m_padStack.thermalSpokeAngle(QtPcbLayerId::FCu), degrees)) {
        m_padStack.setThermalSpokeAngle(degrees, QtPcbLayerId::FCu);
        // Signal emission removed
    }
}

std::optional<int> EDA_PAD_DATA::getLocalThermalGap() const
{
    return m_padStack.thermalGap(QtPcbLayerId::FCu);
}

void EDA_PAD_DATA::setLocalThermalGap(std::optional<int> gap)
{
    if (m_padStack.thermalGap(QtPcbLayerId::FCu) != gap) {
        m_padStack.setThermalGap(gap, QtPcbLayerId::FCu);
        // Signal emission removed
    }
}

//=============================================================================
// Pad to Die Length
//=============================================================================

void EDA_PAD_DATA::setPadToDieLength(int length)
{
    if (m_padToDieLength != length) {
        m_padToDieLength = length;
        // Signal emission removed
        // Signal emission removed
    }
}

//=============================================================================
// Shape Generation and Geometry
//=============================================================================

std::shared_ptr<QtShape> EDA_PAD_DATA::getEffectiveShape(QtPcbLayerId layer, QtFlashing flash) const
{
    Q_UNUSED(flash)
    
    if (m_shapesDirty) {
        buildEffectiveShapes();
    }
    
    auto it = m_effectiveShapes.find(layer);
    if (it != m_effectiveShapes.end()) {
        // Convert QSharedPointer to std::shared_ptr
        QSharedPointer<QtShapeCompound> qPtr = it.value();
        return std::shared_ptr<QtShape>(qPtr.data(), [qPtr](QtShape*) mutable { qPtr.reset(); });
    }

    // Build shape for this layer if not cached
    QSharedPointer<QtShapeCompound> qPtr = buildEffectiveShapeForLayer(layer);
    return std::shared_ptr<QtShape>(qPtr.data(), [qPtr](QtShape*) mutable { qPtr.reset(); });
}

std::shared_ptr<QtShapeSegment> EDA_PAD_DATA::getEffectiveHoleShape() const
{
    if (m_shapesDirty) {
        buildEffectiveShapes();
    }

    // Convert QSharedPointer to std::shared_ptr
    QSharedPointer<QtShapeSegment> qPtr = m_effectiveHoleShape;
    return std::shared_ptr<QtShapeSegment>(qPtr.data(), [qPtr](QtShapeSegment*) mutable { qPtr.reset(); });
}

int EDA_PAD_DATA::getBoundingRadius() const
{
    if (m_shapesDirty) {
        buildEffectiveShapes();
    }
    
    return m_effectiveBoundingRadius;
}

//=============================================================================
// Polygon Transformation
//=============================================================================

void EDA_PAD_DATA::transformShapeToPolygon(QtShapePolySet& buffer, QtPcbLayerId layer,
                                    double clearance, double error, 
                                    QtErrorLoc errorLocation, bool ignoreLineWidth) const
{
    Q_UNUSED(buffer)
    Q_UNUSED(layer)
    Q_UNUSED(clearance)
    Q_UNUSED(error)
    Q_UNUSED(errorLocation)
    Q_UNUSED(ignoreLineWidth)
    
    // TODO: Implement polygon transformation
    qCWarning(lcEDA_PAD_DATA) << "transformShapeToPolygon not yet implemented";
}

bool EDA_PAD_DATA::transformHoleToPolygon(QtShapePolySet& buffer, double clearance, double error,
                                   QtErrorLoc errorLocation) const
{
    Q_UNUSED(buffer)
    Q_UNUSED(clearance)
    Q_UNUSED(error)
    Q_UNUSED(errorLocation)
    
    if (!hasHole()) {
        return false;
    }
    
    // TODO: Implement hole polygon transformation
    qCWarning(lcEDA_PAD_DATA) << "transformHoleToPolygon not yet implemented";
    return true;
}

//=============================================================================
// Aperture Support
//=============================================================================

bool EDA_PAD_DATA::isAperturePad() const
{
    QtLayerSet copperLayers = QtLayerSet::allCopperLayers();
    return (m_padStack.layerSet() & copperLayers).empty();
}

//=============================================================================
// Layer Flashing
//=============================================================================

bool EDA_PAD_DATA::flashLayer(QtPcbLayerId layer, bool onlyCheckIfPermitted) const
{
    Q_UNUSED(onlyCheckIfPermitted)
    
    if (!m_padStack.layerSet().test(layer)) {
        return false;
    }
    
    return !conditionallyFlashed(layer);
}

bool EDA_PAD_DATA::flashLayer(const QtLayerSet& layers) const
{
    QtLayerSet padLayers = m_padStack.layerSet();
    QtLayerSet intersection = padLayers & layers;
    
    return !intersection.empty();
}

bool EDA_PAD_DATA::conditionallyFlashed(QtPcbLayerId layer) const
{
    switch (m_padStack.unconnectedLayerMode()) {
    case QtUnconnectedLayerMode::KeepAll:
        return false;
        
    case QtUnconnectedLayerMode::RemoveAll:
        return true;
        
    case QtUnconnectedLayerMode::RemoveExceptStartAndEnd:
        QtDrillProperties drill = m_padStack.drill();
        if (layer == drill.startLayer || layer == drill.endLayer) {
            return false;
        }
        return true;
    }
    
    return false;
}

//=============================================================================
// Unconnected Layer Handling
//=============================================================================

QtUnconnectedLayerMode EDA_PAD_DATA::getUnconnectedLayerMode() const
{
    return m_padStack.unconnectedLayerMode();
}

void EDA_PAD_DATA::setUnconnectedLayerMode(QtUnconnectedLayerMode mode)
{
    if (m_padStack.unconnectedLayerMode() != mode) {
        m_padStack.setUnconnectedLayerMode(mode);
        setDirty();
        // Signal emission removed
    }
}

//=============================================================================
// Hit Testing
//=============================================================================

bool EDA_PAD_DATA::hitTest(const QPointF& position, double accuracy) const
{
    QRectF bbox = getBoundingBox();
    bbox.adjust(-accuracy, -accuracy, accuracy, accuracy);
    
    if (!bbox.contains(position)) {
        return false;
    }
    
    // TODO: Implement detailed shape-based hit testing
    return true;
}

bool EDA_PAD_DATA::hitTest(const QRectF& rect, bool contained, double accuracy) const
{
    QRectF bbox = getBoundingBox();
    bbox.adjust(-accuracy, -accuracy, accuracy, accuracy);
    
    if (contained) {
        return rect.contains(bbox);
    } else {
        return rect.intersects(bbox);
    }
}

//=============================================================================
// Bounding Box and Visualization
//=============================================================================

QRectF EDA_PAD_DATA::getBoundingBox() const
{
    if (m_shapesDirty) {
        buildEffectiveShapes();
    }
    
    return m_effectiveBoundingBox;
}

//=============================================================================
// Settings Import
//=============================================================================

void EDA_PAD_DATA::importSettingsFrom(const EDA_PAD_DATA& masterPad)
{
    // Import all settings except position, number, and parent
    QString oldNumber = m_number;
    QPointF oldPosition = m_position;
    
    *this = masterPad;
    
    // Restore position and number
    m_number = oldNumber;
    m_position = oldPosition;
    
    setDirty();
    // Signal emission removed
}

//=============================================================================
// Duplication and Comparison
//=============================================================================

EDA_BOARD_OBJECT_DATA* EDA_PAD_DATA::duplicate() const
{
    return new EDA_PAD_DATA(*this);
}

double EDA_PAD_DATA::similarity(const EDA_BOARD_OBJECT_DATA& other) const
{
    const EDA_PAD_DATA* otherPad = dynamic_cast<const EDA_PAD_DATA*>(&other);
    if (!otherPad) {
        return 0.0;
    }
    
    // Simple similarity based on key properties
    double score = 0.0;
    
    if (m_attribute == otherPad->m_attribute) score += 0.2;
    if (m_property == otherPad->m_property) score += 0.1;
    if (getShape() == otherPad->getShape()) score += 0.3;
    if (qFuzzyCompare(getSize().width(), otherPad->getSize().width()) &&
        qFuzzyCompare(getSize().height(), otherPad->getSize().height())) score += 0.2;
    if (qFuzzyCompare(getDrillSize().width(), otherPad->getDrillSize().width()) &&
        qFuzzyCompare(getDrillSize().height(), otherPad->getDrillSize().height())) score += 0.2;
    
    return score;
}

bool EDA_PAD_DATA::operator==(const EDA_BOARD_OBJECT_DATA& other) const
{
    const EDA_PAD_DATA* otherPad = dynamic_cast<const EDA_PAD_DATA*>(&other);
    return otherPad && (*this == *otherPad);
}

bool EDA_PAD_DATA::operator==(const EDA_PAD_DATA& other) const
{
    return EDA_BOARD_CONNECTED_OBJECT::operator==(other) &&
           m_number == other.m_number &&
           m_pinFunction == other.m_pinFunction &&
           m_pinType == other.m_pinType &&
           m_position == other.m_position &&
           m_attribute == other.m_attribute &&
           m_property == other.m_property &&
           m_padToDieLength == other.m_padToDieLength &&
           m_padStack == other.m_padStack;
}

//=============================================================================
// Dirty State Management
//=============================================================================

void EDA_PAD_DATA::setDirty()
{
    m_shapesDirty = true;
    m_polyDirty = true;
    clearCaches();
}

void EDA_PAD_DATA::buildEffectiveShapes() const
{
    QMutexLocker locker(&m_shapesBuildingLock);
    
    if (!m_shapesDirty) {
        return;
    }
    
    
    // Clear existing shapes
    m_effectiveShapes.clear();
    m_effectiveHoleShape.reset();
    
    // Build bounding box
    QSizeF size = getSize();
    QPointF pos = getPosition();
    
    m_effectiveBoundingBox = QRectF(
        pos.x() - size.width() / 2.0,
        pos.y() - size.height() / 2.0,
        size.width(),
        size.height()
    );
    
    // Calculate bounding radius
    m_effectiveBoundingRadius = static_cast<int>(
        std::sqrt(size.width() * size.width() + size.height() * size.height()) / 2.0
    );
    
    // Build hole shape if present
    if (hasHole()) {
        // TODO: Create effective hole shape
    }
    
    m_shapesDirty = false;
}

void EDA_PAD_DATA::buildEffectivePolygon() const
{
    QMutexLocker locker(&m_polyBuildingLock);
    
    if (!m_polyDirty) {
        return;
    }
    
    
    // TODO: Build polygon representations
    
    m_polyDirty = false;
}

//=============================================================================
// Qt Serialization and Description
//=============================================================================

QVariantMap EDA_PAD_DATA::toVariantMap() const
{
    QVariantMap map = EDA_BOARD_CONNECTED_OBJECT::toVariantMap();
    
    map["number"] = m_number;
    map["pinFunction"] = m_pinFunction;
    map["pinType"] = m_pinType;
    map["position"] = QVariantList{m_position.x(), m_position.y()};
    map["attribute"] = static_cast<int>(m_attribute);
    map["property"] = static_cast<int>(m_property);
    map["padToDieLength"] = m_padToDieLength;
    map["padStack"] = m_padStack.toVariantMap();
    map["subRatsnest"] = m_subRatsnest;
    
    return map;
}

void EDA_PAD_DATA::fromVariantMap(const QVariantMap& map)
{
    EDA_BOARD_CONNECTED_OBJECT::fromVariantMap(map);
    
    m_number = map.value("number").toString();
    m_pinFunction = map.value("pinFunction").toString();
    m_pinType = map.value("pinType").toString();
    
    QVariantList posList = map.value("position").toList();
    if (posList.size() >= 2) {
        m_position = QPointF(posList[0].toDouble(), posList[1].toDouble());
    }
    
    m_attribute = static_cast<QtPadAttribute>(map.value("attribute", 0).toInt());
    m_property = static_cast<QtPadProperty>(map.value("property", 0).toInt());
    m_padToDieLength = map.value("padToDieLength", 0).toInt();
    m_padStack.fromVariantMap(map.value("padStack").toMap());
    m_subRatsnest = map.value("subRatsnest", 0).toInt();
    
    setDirty();
}

QString EDA_PAD_DATA::getItemDescription(bool full) const
{
    QString desc = QStringLiteral("Pad");
    
    if (!m_number.isEmpty()) {
        desc += QStringLiteral(" %1").arg(m_number);
    }
    
    if (full) {
        desc += QStringLiteral(" (%1, %2)").arg(getShapeName()).arg(getAttributeName());
        
        if (!m_pinFunction.isEmpty()) {
            desc += QStringLiteral(" [%1]").arg(m_pinFunction);
        }
    }
    
    return desc;
}

QString EDA_PAD_DATA::toString() const
{
    return QStringLiteral("EDA_PAD_DATA(number='%1', shape=%2, size=%3x%4, pos=%5,%6)")
           .arg(m_number)
           .arg(static_cast<int>(getShape()))
           .arg(getSize().width())
           .arg(getSize().height())
           .arg(m_position.x())
           .arg(m_position.y());
}

//=============================================================================
// Static Utilities
//=============================================================================

QtLayerSet EDA_PAD_DATA::PTHMask()
{
    return QtLayerSet::allCopperLayers() | 
           QtLayerSet(QtPcbLayerId::FMask) | 
           QtLayerSet(QtPcbLayerId::BMask);
}

QtLayerSet EDA_PAD_DATA::SMDMask()
{
    return QtLayerSet(QtPcbLayerId::FCu) | 
           QtLayerSet(QtPcbLayerId::FMask) | 
           QtLayerSet(QtPcbLayerId::FPaste);
}

QtLayerSet EDA_PAD_DATA::ConnectorSMDMask()
{
    return QtLayerSet(QtPcbLayerId::FCu) | 
           QtLayerSet(QtPcbLayerId::FMask);
}

QtLayerSet EDA_PAD_DATA::UnplatedHoleMask()
{
    return QtLayerSet(QtPcbLayerId::FCu) | 
           QtLayerSet(QtPcbLayerId::BCu) | 
           QtLayerSet(QtPcbLayerId::FMask) | 
           QtLayerSet(QtPcbLayerId::BMask);
}

QtLayerSet EDA_PAD_DATA::ApertureMask()
{
    return QtLayerSet(QtPcbLayerId::FPaste) | 
           QtLayerSet(QtPcbLayerId::BPaste);
}

int EDA_PAD_DATA::comparePads(const EDA_PAD_DATA* padRef, const EDA_PAD_DATA* padCmp)
{
    if (!padRef || !padCmp) {
        return padRef ? 1 : (padCmp ? -1 : 0);
    }
    
    // Compare by number first
    int numCompare = padRef->m_number.compare(padCmp->m_number);
    if (numCompare != 0) {
        return numCompare;
    }
    
    // Then by position
    if (padRef->m_position.x() != padCmp->m_position.x()) {
        return padRef->m_position.x() < padCmp->m_position.x() ? -1 : 1;
    }
    
    if (padRef->m_position.y() != padCmp->m_position.y()) {
        return padRef->m_position.y() < padCmp->m_position.y() ? -1 : 1;
    }
    
    return 0;
}

QString EDA_PAD_DATA::getShapeName(QtPcbLayerId layer) const
{
    return QtPadUtils::getShapeCanonicalName(getShape(layer));
}

QString EDA_PAD_DATA::getAttributeName() const
{
    return QtPadUtils::getAttributeCanonicalName(m_attribute);
}

//=============================================================================
// Protected Helper Methods
//=============================================================================

void EDA_PAD_DATA::swapData(EDA_BOARD_OBJECT_DATA* other)
{
    EDA_PAD_DATA* otherPad = dynamic_cast<EDA_PAD_DATA*>(other);
    if (!otherPad) {
        return;
    }
    
    EDA_BOARD_CONNECTED_OBJECT::swapData(other);
    
    std::swap(m_number, otherPad->m_number);
    std::swap(m_pinFunction, otherPad->m_pinFunction);
    std::swap(m_pinType, otherPad->m_pinType);
    std::swap(m_position, otherPad->m_position);
    std::swap(m_attribute, otherPad->m_attribute);
    std::swap(m_property, otherPad->m_property);
    std::swap(m_padToDieLength, otherPad->m_padToDieLength);
    std::swap(m_padStack, otherPad->m_padStack);
    std::swap(m_subRatsnest, otherPad->m_subRatsnest);
    
    setDirty();
    otherPad->setDirty();
}

void EDA_PAD_DATA::clearCaches() const
{
    m_effectiveShapes.clear();
    m_effectiveHoleShape.reset();
    m_effectivePolygons.clear();
    m_effectiveBoundingBox = QRectF();
    m_effectiveBoundingRadius = 0;
}

void EDA_PAD_DATA::notifyGeometryChange()
{
    // Signal emission removed
    // Signal emission removed
}

QSharedPointer<QtShapeCompound> EDA_PAD_DATA::buildEffectiveShapeForLayer(QtPcbLayerId layer) const
{
    Q_UNUSED(layer)
    
    // TODO: Implement shape building for specific layer
    qCWarning(lcEDA_PAD_DATA) << "buildEffectiveShapeForLayer not yet implemented";
    return QSharedPointer<QtShapeCompound>();
}

void EDA_PAD_DATA::addPrimitivesToPolygon(QtPcbLayerId layer, QtShapePolySet* mergedPolygon, 
                                   double error, QtErrorLoc errorLocation) const
{
    Q_UNUSED(layer)
    Q_UNUSED(mergedPolygon)
    Q_UNUSED(error)
    Q_UNUSED(errorLocation)
    
    // TODO: Implement primitive polygon merging
    qCWarning(lcEDA_PAD_DATA) << "addPrimitivesToPolygon not yet implemented";
}

//=============================================================================
// QtPadUtils Namespace Implementation
//=============================================================================

namespace QtPadUtils {

QString getShapeCanonicalName(QtPadShape shape)
{
    switch (shape) {
        case QtPadShape::Circle: return "circle";
        case QtPadShape::Rectangle: return "rect";
        case QtPadShape::Oval: return "oval";
        case QtPadShape::Trapezoid: return "trapezoid";
        case QtPadShape::RoundRect: return "roundrect";
        case QtPadShape::ChamferedRect: return "chamfered";
        case QtPadShape::Custom: return "custom";
        default: return "circle";
    }
}

QtPadShape getShapeFromCanonicalName(const QString& name)
{
    if (name == "circle") return QtPadShape::Circle;
    if (name == "rect") return QtPadShape::Rectangle;
    if (name == "oval") return QtPadShape::Oval;
    if (name == "trapezoid") return QtPadShape::Trapezoid;
    if (name == "roundrect") return QtPadShape::RoundRect;
    if (name == "chamfered") return QtPadShape::ChamferedRect;
    if (name == "custom") return QtPadShape::Custom;
    return QtPadShape::Circle;
}

QString getAttributeCanonicalName(QtPadAttribute attribute)
{
    switch (attribute) {
        case QtPadAttribute::PTH: return "thru_hole";
        case QtPadAttribute::SMD: return "smd";
        case QtPadAttribute::Connector: return "connect";
        case QtPadAttribute::NPTH: return "np_thru_hole";
        default: return "thru_hole";
    }
}

QtPadAttribute getAttributeFromCanonicalName(const QString& name)
{
    if (name == "thru_hole") return QtPadAttribute::PTH;
    if (name == "smd") return QtPadAttribute::SMD;
    if (name == "connect") return QtPadAttribute::Connector;
    if (name == "np_thru_hole") return QtPadAttribute::NPTH;
    return QtPadAttribute::PTH;
}

bool QtPadUtils::validatePadConfiguration(const EDA_PAD_DATA* pad, QString* error)
{
    if (!pad) {
        if (error) *error = QCoreApplication::translate("QtPadUtils", "Null pad pointer");
        return false;
    }
    
    // Validate size
    QSizeF size = pad->getSize();
    if (size.width() <= 0.0 || size.height() <= 0.0) {
        if (error) *error = QCoreApplication::translate("QtPadUtils", "Invalid pad size");
        return false;
    }
    
    // Validate drill for PTH and NPTH pads
    if ((pad->getAttribute() == QtPadAttribute::PTH || pad->getAttribute() == QtPadAttribute::NPTH) &&
        !pad->hasHole()) {
        if (error) *error = QCoreApplication::translate("QtPadUtils", "Through-hole pad must have drill");
        return false;
    }
    
    return true;
}

double QtPadUtils::calculatePadArea(const EDA_PAD_DATA* pad, QtPcbLayerId layer)
{
    if (!pad) return 0.0;
    
    QSizeF size = pad->getSize(layer);
    QtPadShape shape = pad->getShape(layer);
    
    switch (shape) {
    case QtPadShape::Circle:
        return M_PI * (size.width() / 2.0) * (size.width() / 2.0);
        
    case QtPadShape::Rectangle:
    case QtPadShape::Trapezoid:
        return size.width() * size.height();
        
    case QtPadShape::Oval:
        // Approximation for oval
        return M_PI * (size.width() / 2.0) * (size.height() / 2.0);
        
    case QtPadShape::RoundRect:
    case QtPadShape::ChamferedRect:
        // Approximation
        return size.width() * size.height() * 0.9;
        
    case QtPadShape::Custom:
        // TODO: Calculate from primitives
        return size.width() * size.height();
    }
    
    return 0.0;
}

bool QtPadUtils::canMergePads(const EDA_PAD_DATA* pad1, const EDA_PAD_DATA* pad2)
{
    if (!pad1 || !pad2) return false;
    
    return pad1->getParent() == pad2->getParent() &&
           pad1->getNumber() == pad2->getNumber() &&
           pad1->getShape() == pad2->getShape() &&
           pad1->getAttribute() == pad2->getAttribute();
}

} // namespace QtPadUtils
