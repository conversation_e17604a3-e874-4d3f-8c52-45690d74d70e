cmake_minimum_required(VERSION 3.16 FATAL_ERROR)

project(qt_pcbnew_migration LANGUAGES CXX)

# Set C++ standard
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Disable Qt MOC processing - using standard C++ classes without Qt object system
set(CMAKE_AUTOMOC OFF)
set(CMAKE_AUTORCC OFF)
set(CMAKE_AUTOUIC OFF)

# Set Qt6 path for MSVC version
set(CMAKE_PREFIX_PATH "C:/Qt/6.9.1/msvc2022_64")

# Find Qt6 packages
find_package(Qt6 REQUIRED COMPONENTS Core Widgets Concurrent)

# Add compiler flags
if(MSVC)
    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} /Zc:__cplusplus /permissive-")
    set(CMAKE_CXX_FLAGS_DEBUG "${CMAKE_CXX_FLAGS_DEBUG} /Od /Zi")
    set(CMAKE_CXX_FLAGS_RELEASE "${CMAKE_CXX_FLAGS_RELEASE} /O2 /DNDEBUG")
    # Add MSVC-specific definitions
    add_definitions(-DNOMINMAX)
    add_definitions(-DWIN32_LEAN_AND_MEAN)
    add_definitions(-D_CRT_SECURE_NO_WARNINGS)
else()
    set(CMAKE_CXX_FLAGS_DEBUG "${CMAKE_CXX_FLAGS_DEBUG} -g -O0")
    set(CMAKE_CXX_FLAGS_RELEASE "${CMAKE_CXX_FLAGS_RELEASE} -O2 -DNDEBUG")
endif()

# Include directories
include_directories(${CMAKE_CURRENT_SOURCE_DIR})

# Collect all source files explicitly
set(SOURCES
    eda_object_data.cpp
    eda_shape_data.cpp
    eda_text_data.cpp
    eda_board_connected_object.cpp
    eda_board_object_container.cpp
    eda_board_object_data.cpp
    eda_padstack_data.cpp
    eda_track_data.cpp
   eda_via_data.cpp
    eda_arc_data.cpp
    eda_pad_data.cpp
    #eda_net_data.cpp
    #eda_connectivity_manager.cpp
    #eda_netinfo_list.cpp
    #eda_board_data.cpp
    #eda_footprint_data.cpp
    eda_zone_data.cpp
    eda_brd_shape_data.cpp
   eda_brd_text_data.cpp
   eda_brd_ttbox_data.cpp
   eda_brd_field_data.cpp
)

set(HEADERS
    eda_object_data.h
    eda_shape_data.h
    eda_text_data.h
    eda_board_connected_object.h
    eda_board_object_container.h
    eda_board_object_data.h
    eda_padstack_data.h
    eda_track_data.h
   eda_via_data.h
    eda_arc_data.h
   eda_pad_data.h
    #eda_net_data.h
    #eda_connectivity_manager.h
    #eda_netinfo_list.h
    #eda_board_data.h
    #eda_footprint_data.h
    eda_zone_data.h
    eda_brd_shape_data.h
    eda_brd_text_data.h
   eda_brd_ttbox_data.h
    eda_brd_field_data.h
    qt_temporary_implementations.h
)

# Create a library with all the migrated Qt classes
add_library(qt_pcbnew_migration STATIC
    ${SOURCES}
    ${HEADERS}
)

# Link Qt libraries
target_link_libraries(qt_pcbnew_migration
    Qt6::Core
    Qt6::Widgets
    Qt6::Concurrent
)

# Set target properties
set_target_properties(qt_pcbnew_migration PROPERTIES
    CXX_STANDARD 17
    CXX_STANDARD_REQUIRED ON
)

# Enable warnings
if(MSVC)
    target_compile_options(qt_pcbnew_migration PRIVATE /W3)
else()
    target_compile_options(qt_pcbnew_migration PRIVATE -Wall -Wextra)
endif()

# Install targets
install(TARGETS qt_pcbnew_migration
    LIBRARY DESTINATION lib
    ARCHIVE DESTINATION lib
    RUNTIME DESTINATION bin
)

# Install headers
install(FILES ${HEADERS}
    DESTINATION include/qt_pcbnew_migration
)